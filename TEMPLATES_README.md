# Alpine.js Templates Documentation

This document explains the template structure and organization for the Student Management Application.

## Template Organization

The application uses Alpine.js templates that are organized into separate files for better maintainability:

### File Structure
```
├── index.html              # Main HTML file with template containers
├── templates.js             # External Alpine.js templates
├── templates/               # Individual template files (optional)
│   ├── mobile-students-list.html
│   ├── desktop-data-table.html
│   └── pagination-controls.html
└── script.js               # Main application logic
```

## Template Files

### 1. `templates.js`
Contains all Alpine.js templates in a centralized JavaScript object:

- **AlpineTemplates.mobileStudentsList**: Mobile view student list template
- **AlpineTemplates.desktopDataTable**: Desktop data table rows template  
- **AlpineTemplates.paginationControls**: Pagination controls template

### 2. Individual Template Files (`templates/` folder)
Separate HTML files for each template (for reference and easier editing):

- `mobile-students-list.html`: Mobile student list template
- `desktop-data-table.html`: Desktop table rows template
- `pagination-controls.html`: Pagination controls template

## Template Containers in HTML

The main HTML file contains placeholder containers where templates are injected:

```html
<!-- Mobile Students Template Container -->
<div id="mobile-students-template">
    <!-- Template will be loaded here -->
</div>

<!-- Desktop Table Template Container -->
<div id="desktop-table-template">
    <!-- Template will be loaded here -->
</div>

<!-- Pagination Template Container -->
<div id="pagination-template">
    <!-- Template will be loaded here -->
</div>
```

## Template Loading Process

1. **Page Load**: When the page loads, `loadAlpineTemplates()` is called
2. **Template Injection**: Templates from `templates.js` are injected into their containers
3. **Alpine.js Processing**: Alpine.js processes the injected templates and makes them reactive

## Template Features

### Mobile Students List Template
- Uses `x-for` to loop through `paginatedData`
- Displays student photo, name, ID, Arabic name, birth date, and grades
- Includes edit and delete action buttons
- Supports click events for bottom sheet display

### Desktop Data Table Template
- Includes empty state template for when no students are found
- Uses `x-for` to generate table rows for each student
- Supports checkbox selection, sorting, and action buttons
- Displays all student information in tabular format

### Pagination Controls Template
- Shows pagination info (e.g., "Showing 1-10 of 25 students")
- Includes previous/next buttons with disabled states
- Generates page number buttons dynamically
- All controls are reactive to Alpine.js data changes

## Benefits of This Approach

1. **Maintainability**: Templates are separated from main HTML and JavaScript
2. **Reusability**: Templates can be easily reused or modified
3. **Organization**: Clear separation of concerns
4. **Performance**: Templates are loaded once and cached
5. **Development**: Easier to edit and debug individual templates

## Editing Templates

To modify a template:

1. **Option 1**: Edit the template in `templates.js`
2. **Option 2**: Edit the individual template file in `templates/` folder and copy to `templates.js`

After editing, refresh the page to see changes.

## Alpine.js Directives Used

- `x-for`: Loop through data arrays
- `x-text`: Display dynamic text content
- `x-if`: Conditional rendering
- `:key`: Unique keys for list items
- `@click`: Click event handlers
- `:style`: Dynamic styling
- `:class`: Dynamic CSS classes
- `:disabled`: Dynamic disabled state

## Template Variables

Templates have access to Alpine.js data:
- `paginatedData`: Current page student data
- `currentPage`: Current pagination page
- `perPage`: Items per page
- `totalPages`: Total number of pages
- `filteredData`: All filtered student data
- Student object properties: `name`, `id`, `photo`, `levelFr`, `levelAr`, etc.
